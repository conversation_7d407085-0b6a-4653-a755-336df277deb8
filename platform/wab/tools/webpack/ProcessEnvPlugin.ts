import { ENV } from "@/wab/client/env";
import { DefinePlugin } from "@rspack/core";

export const REQUIRED_VAR = Symbol();
export const OPTIONAL_VAR = Symbol();

/**
 * Converts real type to config type.
 *
 * Examples:
 *  `string` -> `string | typeof REQUIRED_VAR`
 *  `string | undefined` -> `string | typeof OPTIONAL_VAR`
 *  `string | null` -> `never` // for simplicity, null is not allowed
 */
type EnvVarValueConfig<T> = null extends T
  ? never
  : undefined extends T
  ? Exclude<T, undefined> | typeof OPTIONAL_VAR
  : T | typeof REQUIRED_VAR;
type EnvConfig = {
  [Key in keyof typeof ENV]: EnvVarValueConfig<typeof ENV[Key]>
};

/**
 * Type-safe way to configure DefinePlugin that matches ENV type.
 *
 * Each KEY will be available as `process.env.KEY` in client code.
 * The value can be:
 * - `FROM_PROCESS_ENV`: Uses the value from process.env at build-time, errors if not found
 * - `OPTIONAL_FROM_PROCESS_ENV`: Uses the value from process.env at build-time, never errors
 * - Any other value: Uses the provided value directly
 */
export class ProcessEnvPlugin extends DefinePlugin {
  constructor(envConfig: EnvConfig) {
    super(
      Object.fromEntries(
        Object.entries(envConfig).map(([key, value]) => {
          const envKey = `process.env.${key}`;

          // In EnvironmentPlugin, undefined = required, null = optional
          const processEnvValue = process.env[key];
          if (value === REQUIRED_VAR) {
            if (!processEnvValue) {
              throw new Error(`Env var ${key} missing`);
            }
            return [envKey, JSON.stringify(processEnvValue)];
          } else if (value === OPTIONAL_VAR) {
            return [
              envKey,
              processEnvValue ? JSON.stringify(processEnvValue) : undefined,
            ];
          } else {
            if (processEnvValue) {
              throw new Error(`Env var ${key} exists but not used`);
            }
            return [envKey, JSON.stringify(value)];
          }
        })
      )
    );
  }
}

type AssertTrue<T extends true> = T;
type AssertFalse<T extends false> = T;
type IsAssignable<Type, AssignableTo> = Type extends AssignableTo ? true : false;

type TestStringAssignableToString = AssertTrue<IsAssignable<string, EnvVarValueConfig<string>>>
type TestRequiredAssignableToString = AssertTrue<IsAssignable<typeof REQUIRED_VAR, EnvVarValueConfig<string>>>
type TestOptionalNotAssignableToString = AssertFalse<IsAssignable<typeof OPTIONAL_VAR, EnvVarValueConfig<string>>>
type TestStringAssignableToUndefined = AssertTrue<IsAssignable<string, EnvVarValueConfig<string | undefined>>>
type TestRequiredAssignableToUndefined = AssertFalse<IsAssignable<typeof REQUIRED_VAR, EnvVarValueConfig<string | undefined>>>
type TestOptionalNotAssignableToUndefined = AssertTrue<IsAssignable<typeof OPTIONAL_VAR, EnvVarValueConfig<string | undefined>>>
type TestStringAssignableToNull = AssertFalse<IsAssignable<string, EnvVarValueConfig<string | null>>>
type TestRequiredAssignableToNull = AssertFalse<IsAssignable<typeof REQUIRED_VAR, EnvVarValueConfig<string | null>>>
type TestOptionalNotAssignableToNull = AssertFalse<IsAssignable<typeof OPTIONAL_VAR, EnvVarValueConfig<string | null>>>
